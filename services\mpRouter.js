const BASE_CACHE = require("@/utils/cache/baseCache")
export function createPathWithParams(path, params) {
  if (!params || typeof params !== "object") return path
  const query = convertPathQuery(params, convertQueryParamValue)
  return query ? `${path}?${query}` : path
}

/**
 * 转换单个参数值，使其适合URL查询字符串。
 * @param {string|object} val - 参数的值。
 * @returns {string} - 转换后的值。
 */
function convertQueryParamValue(val) {
  if (typeof val === "object") {
    return JSON.stringify(val)
  } else if (typeof val === "string" && val?.indexOf("://") >= 0) {
    return encodeURIComponent(val)
  }
  return val
}

/**
 * 遍历参数对象，并使用convertQueryParamValue函数转换每个值。
 * @param {Object} params - 包含查询参数的对象。
 * @returns {string} - 转换后的查询字符串。
 */
export function convertPathQuery(params, call) {
  return Object.keys(params)
    .map((key) => {
      if (typeof call === "function") {
        return `${key}=${call(params[key])}`
      }
      return `${key}=${params[key]}`
    })
    .join("&")
}
export function mergeUrlParams(url, params) {
  // 拆分URL为路径和查询部分
  const [path, queryString] = url.split("?")

  // 解析原有的查询参数
  const searchParams = {}
  if (queryString) {
    queryString.split("&").forEach((pair) => {
      const [key, value] = pair.split("=")
      if (key) {
        // 解码键和值
        const decodedKey = decodeURIComponent(key)
        const decodedValue = decodeURIComponent(value || "")
        searchParams[decodedKey] = decodedValue
      }
    })
  }

  // 过滤掉params中值为undefined或null的参数
  const filteredParams = {}
  Object.keys(params).forEach((key) => {
    const value = params[key]
    if (value !== undefined && value !== null) {
      filteredParams[key] = value
    }
  })

  // 合并参数，后者覆盖前者
  const mergedParams = { ...searchParams, ...filteredParams }

  // 将参数转换为查询字符串
  const queryArray = Object.keys(mergedParams).map((key) => {
    // 编码键和值
    const encodedKey = encodeURIComponent(key)
    const encodedValue = encodeURIComponent(mergedParams[key])
    return `${encodedKey}=${encodedValue}`
  })

  const newQueryString = queryArray.join("&")

  // 拼接路径和查询字符串
  return newQueryString ? `${path}?${newQueryString}` : path
}

function navigate({ api, data = {} }) {
  const { path, query } = data

  // 检查路径是否有效
  if (!path && api !== "navigateBack") throw new Error("路径传参有误")
  const defaultQuery = {}
  const baseCache = BASE_CACHE.getBaseCache()
  if (baseCache.campus?.id) {
    defaultQuery.campus_id = baseCache.campus.id
  }
  if (baseCache.province?.key) {
    defaultQuery.province = baseCache.province.key
  }

  let newQuery = query
  if (!query?.province && !Number(query?.campus_id)) {
    newQuery = Object.assign(defaultQuery, query || {})
  }

  // 构建完整的URL
  const url = createPathWithParams(path, newQuery)

  return wx[api]({ url })
}

/**
 * 检查并处理页面栈长度
 * 当页面栈超过6个页面时，检查是否有重复的页面路径
 * 如果即将导航的页面在栈中已存在，则先返回到该页面
 */
function checkPageStackLimit(targetPath) {
  const pages = getCurrentPages()
  console.log(pages)

  // 如果页面栈长度不超过5个（加上即将导航的页面就是6个），不需要处理
  if (pages.length <= 5) {
    return false
  }

  // 获取目标页面的基础路径（不包含参数）
  const targetBasePath = targetPath.split("?")[0]

  // 检查目标页面是否已经在页面栈中存在
  let existingPageIndex = -1
  for (let i = pages.length - 1; i >= 0; i--) {
    const route = pages[i].route || ""
    const basePath = route.split("?")[0]

    if (basePath === targetBasePath) {
      existingPageIndex = i
      break
    }
  }

  // 如果目标页面已存在，返回到该页面而不是新建
  if (existingPageIndex !== -1) {
    const delta = pages.length - existingPageIndex
    console.log(`目标页面已存在于页面栈中，返回到该页面，delta: ${delta}`)

    if (delta > 1) {
      wx.navigateBack({ delta: delta - 1 })
      return true // 表示已处理，不需要继续导航
    }
  }

  // 如果页面栈长度超过6个且目标页面不存在，尝试清理重复页面
  if (pages.length >= 6) {
    const pathIndexMap = new Map()
    const duplicateIndexes = []

    // 找出重复的页面路径，记录较早的实例索引
    pages.forEach((page, index) => {
      const route = page.route || ""
      const basePath = route.split("?")[0]

      if (pathIndexMap.has(basePath)) {
        // 如果路径已存在，将之前的索引标记为重复
        duplicateIndexes.push(pathIndexMap.get(basePath))
      }

      // 更新为最新的索引
      pathIndexMap.set(basePath, index)
    })

    // 如果有重复页面，尝试清理最早的重复页面
    if (duplicateIndexes.length > 0) {
      // 找到最早的重复页面（索引最小的）
      const earliestDuplicateIndex = Math.min(...duplicateIndexes)
      const deltaToEarliest = pages.length - earliestDuplicateIndex

      console.log(
        `检测到${duplicateIndexes.length}个重复页面，清理最早的重复页面，索引: ${earliestDuplicateIndex}`
      )

      // 如果最早的重复页面不是当前页面，则返回到它之后的页面
      if (deltaToEarliest > 1 && deltaToEarliest <= pages.length) {
        wx.navigateBack({ delta: deltaToEarliest })
        return true // 表示已处理，不需要继续导航
      }
    }
  }

  return false
}

export function navigateTo(data = {}) {
  const { path } = data

  checkPageStackLimit(path)

  return navigate({ api: "navigateTo", data })
}

export function redirectTo(data = {}) {
  return navigate({ api: "redirectTo", data })
}

export function switchTab(data = {}) {
  return navigate({ api: "switchTab", data })
}

export function navigateBack(data = {}) {
  if (getCurrentPages().length <= 1) {
    reLaunch({ path: "/pages/index/index" })
    return
  }
  return navigate({ api: "navigateBack", data })
}

export function reLaunch(data = {}) {
  return navigate({ api: "reLaunch", data })
}
